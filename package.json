{"description": "胡建大卖家Temu插件", "devDependencies": {"@crxjs/vite-plugin": "^2.0.0-beta.33", "@eslint/compat": "^1.2.9", "@eslint/js": "^9.28.0", "@iconify-json/lucide": "^1.2.45", "@iconify-json/ph": "^1.2.2", "@iconify-json/svg-spinners": "^1.2.2", "@intlify/unplugin-vue-i18n": "^6.0.8", "@tailwindcss/typography": "^0.5.16", "@types/eslint": "^9.6.1", "@types/node": "^22.15.29", "@types/webextension-polyfill": "^0.12.3", "@vitejs/plugin-vue": "^5.2.4", "@vue/compiler-sfc": "^3.5.16", "@vueuse/core": "^13.3.0", "chalk": "^5.4.1", "chrome-types": "^0.1.352", "commander": "^14.0.0", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "eslint": "^9.28.0", "eslint-plugin-vue": "^10.1.0", "get-installed-browsers": "^0.1.7", "globals": "^16.2.0", "marked": "^15.0.12", "pinia": "^3.0.2", "prettier": "^3.5.3", "tailwindcss": "^4.1.8", "terser": "^5.40.0", "tsx": "^4.19.4", "tw-animate-css": "^1.3.3", "typescript": "^5.8.3", "typescript-eslint": "^8.33.0", "unplugin-auto-import": "^19.3.0", "unplugin-imagemin": "^0.6.7", "unplugin-turbo-console": "^2.1.3", "unplugin-vue-components": "^28.7.0", "unplugin-vue-router": "^0.12.0", "vite": "^6.3.5", "vite-plugin-vue-devtools": "^7.7.6", "vite-plugin-zip-pack": "^1.2.4", "vue": "^3.5.16", "vue-i18n": "^11.1.5", "vue-router": "^4.5.1", "vue-tsc": "^2.2.10", "web-ext": "^8.7.1", "webext-bridge": "^6.0.1", "webextension-polyfill": "^0.12.0"}, "displayName": "胡建大卖家", "name": "胡建大卖家Temu", "overrides": {"@crxjs/vite-plugin": "$@crxjs/vite-plugin"}, "private": true, "scripts": {"build": "npm run build:chrome && npm run build:firefox", "build:chrome": "cross-env NODE_ENV=production vite build -c vite.chrome.config.ts", "build:firefox": "cross-env NODE_ENV=production vite build -c vite.firefox.config.ts", "dev": "concurrently \"npm run dev:chrome\" \"npm run dev:firefox\"", "dev:chrome": "cross-env NODE_ENV=development vite -c vite.chrome.config.ts", "dev:firefox": "cross-env NODE_ENV=development vite build --mode development --watch -c vite.firefox.config.ts", "format": "prettier --write .", "launch": "tsx scripts/launch.ts", "launch:all": "tsx scripts/launch.ts --all", "lint": "eslint . --fix --cache", "lint:manifest": "web-ext lint --pretty", "typecheck": "vue-tsc --noEmit"}, "type": "module", "version": "0.1.0", "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.511.0", "tailwind-merge": "^3.3.0", "vite-plugin-static-copy": "^3.0.0"}}