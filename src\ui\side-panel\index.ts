import { i18n } from "src/utils/i18n"
import { pinia } from "src/utils/pinia"
import { appRouter } from "src/utils/router"
import { createApp } from "vue"
import App from "./app.vue"
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import "./index.css"

appRouter.addRoute({
  path: "/",
  redirect: "/side-panel",
})

// 添加默认重定向到首页
appRouter.addRoute({
  path: "/side-panel",
  redirect: "/side-panel/dashboard",
})

const app = createApp(App).use(i18n).use(Antd).use(pinia).use(appRouter)

app.mount("#app")

// 移除可能导致aria-hidden问题的属性
const removeAriaHidden = () => {
  const appElement = document.getElementById('app')
  if (appElement) {
    appElement.removeAttribute('aria-hidden')
    appElement.removeAttribute('data-aria-hidden')

    // 监听DOM变化，持续移除aria-hidden
    const observer = new MutationObserver(() => {
      if (appElement.hasAttribute('aria-hidden')) {
        appElement.removeAttribute('aria-hidden')
      }
      if (appElement.hasAttribute('data-aria-hidden')) {
        appElement.removeAttribute('data-aria-hidden')
      }
    })

    observer.observe(appElement, {
      attributes: true,
      attributeFilter: ['aria-hidden', 'data-aria-hidden']
    })
  }
}

// 在下一个tick执行，确保DOM已经渲染
setTimeout(removeAriaHidden, 0)

export default app

self.onerror = function (message, source, lineno, colno, error) {
  console.info("Error: " + message)
  console.info("Source: " + source)
  console.info("Line: " + lineno)
  console.info("Column: " + colno)
  console.info("Error object: " + error)
}
