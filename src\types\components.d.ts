/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AmazonDataPreviewModal: typeof import('./../components/AmazonDataPreviewModal.vue')['default']
    AppFooter: typeof import('./../components/AppFooter.vue')['default']
    AppHeader: typeof import('./../components/AppHeader.vue')['default']
    LoginModal: typeof import('./../components/LoginModal.vue')['default']
    NotificationToast: typeof import('./../components/NotificationToast.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterLinkUp: typeof import('./../components/RouterLinkUp.vue')['default']
    RouterView: typeof import('vue-router')['RouterView']
    StateDisplayError: typeof import('./../components/state/DisplayError.vue')['default']
    StateLoadingSpinner: typeof import('./../components/state/LoadingSpinner.vue')['default']
    StateTailwindEmptyState: typeof import('./../components/state/tailwind-empty-state.vue')['default']
    TemuNotFound: typeof import('./../components/TemuNotFound.vue')['default']
    TestComponent: typeof import('./../components/TestComponent.vue')['default']
    ThemeSwitch: typeof import('./../components/ThemeSwitch.vue')['default']
    TopNavbar: typeof import('./../components/TopNavbar.vue')['default']
  }
}
