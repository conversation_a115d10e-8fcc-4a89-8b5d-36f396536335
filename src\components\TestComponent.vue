<script setup lang="ts">
import { MinusOutlined, PlusOutlined } from '@ant-design/icons-vue'

const testStore = useTestStore()
const { increment, decrement } = testStore
const { count, name } = storeToRefs(testStore)
</script>

<template>
  <div>
    <!-- Counter Component -->
    <div class="text-center">
      <div>
        <div class="text-lg font-semibold mb-4">Name: {{ name }}</div>
        <a-input
          v-model:value="name"
          type="text"
        />
      </div>
      <br />
      <div class="text-lg font-semibold mb-4">Count: {{ count }}</div>
      <div class="flex gap-2 justify-center">
        <a-button
          @click="decrement"
        >
          <template #icon>
            <MinusOutlined />
          </template>
          Decrement
        </a-button>
        <a-button
          @click="increment"
        >
          <template #icon>
            <PlusOutlined />
          </template>
          Increment
        </a-button>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
