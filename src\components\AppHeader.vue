<script setup lang="ts">
import { QuestionCircleOutlined, SettingOutlined } from '@ant-design/icons-vue'
import ThemeSwitch from '@/components/ThemeSwitch.vue'
</script>

<template>
  <div class="flex justify-between gap-4 p-2 bg-neutral">
    <RouterLink
      to="/"
      class="flex gap-2 items-center"
    >
      <img
        src="@assets/logo.png"
        alt="logo"
        class="h-8 w-auto"
      />
      <div class="font-semibold text-primary">
        Vite Vue 3 Chrome Extension
      </div>
    </RouterLink>
    <div class="flex gap-2 justify-center">
      <a-button
        type="text"
        @click="$router.push('/common/about')"
      >
        <template #icon>
          <QuestionCircleOutlined />
        </template>
      </a-button>
      <a-button
        type="text"
        @click="$router.push('/options-page')"
      >
        <template #icon>
          <SettingOutlined />
        </template>
      </a-button>
      <ThemeSwitch />
    </div>
  </div>
</template>

<style scoped></style>
