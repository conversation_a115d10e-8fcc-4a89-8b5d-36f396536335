@import "../../assets/base.css";

/* Ant Design Vue 基础样式补充 */
.ant-layout-header {
  background: #fff;
  padding: 0;
  height: auto;
}

.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn-primary {
  background: #1677ff;
  border-color: #1677ff;
}

.ant-btn-text {
  color: rgba(0, 0, 0, 0.88);
}

.ant-btn-text:hover {
  background: rgba(0, 0, 0, 0.06);
}

.ant-avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.ant-space {
  display: inline-flex;
  align-items: center;
}

.ant-space-item {
  display: flex;
  align-items: center;
}

.ant-dropdown {
  z-index: 1050;
}

.ant-tag {
  display: inline-flex;
  align-items: center;
  padding: 0 7px;
  font-size: 12px;
  line-height: 20px;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.ant-tag-blue {
  color: #1677ff;
  background: #f0f5ff;
  border-color: #adc6ff;
}

/* 修复下拉菜单显示问题 */
[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
}

/* 确保下拉菜单内容可见 */
.ui-select-content,
[data-radix-select-content] {
  z-index: 9999 !important;
  position: fixed !important;
}

/* 修复可能的容器溢出问题 */
.ui-select-trigger,
[data-radix-select-trigger] {
  position: relative;
}

/* 确保下拉菜单不被遮挡 */
body {
  overflow: visible !important;
}

#app {
  overflow: visible !important;
}

/* 移除可能导致aria-hidden问题的属性 */
[aria-hidden="true"] {
  aria-hidden: false !important;
}
