# 使用 Vue 3 和 Ant Design Vue 开发 Chrome 扩展 (Manifest V3) 的核心要求与实践指南

本文档旨在总结使用 Vue 3 和 Ant Design Vue 开发 Chrome 扩展（Manifest V3）时，需要特别注意的核心编码要求和常见陷阱，涵盖从配置、开发到打包的全过程。

## 1. 内容安全策略 (CSP - Content Security Policy)

这是从 Manifest V2 迁移到 V3 时最重要、最棘手的变化。Manifest V3 强制执行了更严格的 CSP，旨在防止跨站脚本攻击 (XSS)。

### 核心限制

*   **禁止内联脚本 (`unsafe-inline`)**:
    *   **错误做法**: `<button onclick="myFunction()">Click</button>` 或 `<script>alert('hello');</script>`。
    *   **Vue 的挑战**: Vue 默认通过内联事件处理器（如 `@click`）和内联样式来工作。
    *   **Ant Design Vue 的挑战**: 为了动态创建样式（例如，根据主题色生成 CSS），它会动态地在 `<head>` 中插入 `<style>` 标签，这会直接违反 CSP 的 `style-src` 规则。

*   **禁止 `eval` (`unsafe-eval`)**:
    *   **错误做法**: `eval('...')`, `new Function('...')`, `setTimeout('...')`。
    *   **Vue 的挑战**: Vue 的模板编译器在某些模式下（特别是运行时编译）可能会使用 `new Function` 来生成渲染函数，这等同于 `eval`。

### 解决方案与最佳实践

#### 1.1 使用预编译 (AOT - Ahead-of-Time Compilation)
**必须要做！** 确保你的构建工具（Vite/Webpack）在打包时将 Vue 的模板（`.vue` 文件）完全编译成 JavaScript 渲染函数。这可以避免在运行时使用 `eval`。幸运的是，所有主流的 Vue 3 脚手架默认就是这么做的。

#### 1.2 处理 Ant Design Vue 的动态样式问题
*   **核心方案：使用 `css-in-js` 的 “Hash-based CSP” 模式。**
*   Ant Design Vue 官方提供了解决方案。你可以在使用 `createApp` 的地方配置 `csp` 选项。

    ```typescript
// main.ts
    import { createApp } from 'vue';
    import Antd from 'ant-design-vue';
    import App from './App.vue';
    
    const app = createApp(App);
    
    // 如果遇到动态样式问题，可以查阅 Antd 最新文档，看是否需要特定 csp 配置。
    // 例如：app.use(Antd, { csp: { nonce: 'your-nonce' } });
    // 但在扩展中，更推荐下面的静态提取方案。
    app.use(Antd);
    
    app.mount('#app');
```
*   **更可靠的方法：提取静态样式文件。** 
    尽可能将所有样式打包成一个或多个独立的 `.css` 文件，并在 `manifest.json` 中声明。这避免了运行时动态插入 `<style>` 标签。对于主题切换等动态需求，可以通过切换预先生成好的 CSS 文件或使用 CSS 变量来实现。

#### 1.3 配置 `manifest.json` 的 `content_security_policy`
*   默认情况下，Manifest V3 的 CSP 是 `script-src 'self'; object-src 'self';`。
*   如果你的扩展需要从远程服务器加载资源，必须明确地将其添加到 CSP 中。

    ```json
"content_security_policy": {
      "extension_pages": "script-src 'self'; object-src 'self';",
      "sandbox": "..." 
    }
```
    加载外部资源的例子：
    ```json
"content_security_policy": {
      "extension_pages": "script-src 'self' https://some-trusted-cdn.com; style-src 'self' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com;"
    }
```

## 2. Background Scripts -> Service Workers

Manifest V3 用 Service Workers 替换了持久的背景页。

### 核心变化

*   **非持久性**: Service Worker 在不活动时会被终止，并在需要时（如监听到事件）被唤醒。
*   **无 DOM 访问**: Service Worker 不能访问 `window`, `document` 或任何 DOM API。
*   **状态管理**: 所有状态（变量、数据）在 Service Worker 终止时都会丢失。

### 解决方案与最佳实践

#### 2.1 使用 `chrome.storage` API
将所有需要持久化的数据（用户设置、缓存信息等）存储在 `chrome.storage.local` 或 `chrome.storage.sync` 中，而不是存储在全局变量里。

#### 2.2 事件驱动模型
你的 Service Worker 应该是无状态的。其主要作用是注册监听器（如 `chrome.runtime.onMessage`, `chrome.alarms.onAlarm`），并在事件触发时执行特定逻辑。

#### 2.3 避免在顶层作用域进行异步操作
*   **错误做法**:
    ```javascript
// service-worker.js
    let userData = await chrome.storage.local.get('user'); // 可能执行太晚
    chrome.runtime.onMessage.addListener( ... );
```
*   **正确做法**: 在事件监听器内部获取数据。
    ```javascript
// service-worker.js
    chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
      if (message.action === 'getUser') {
        const data = await chrome.storage.local.get('user');
        sendResponse(data.user);
      }
      return true; // 保持消息通道开放以进行异步响应
    });
```

## 3. 远程代码执行限制

*   **`"world": "MAIN"` 的使用**：
    *   这是向页面注入拦截逻辑等代码的最佳方式。
    *   **要求**: 被注入的 JS 文件**不能**依赖开发服务器（HMR）。必须在**生产构建 (`npm run build`)** 后，加载打包好的 `dist` 目录进行测试，以确保其行为和最终发布版本一致。

## 4. API 变更

*   **回调 -> Promise**: 大部分 `chrome.*` API 现在返回 Promise，使得代码可以用 `async/await` 编写，更清晰。
    *   **旧版**: `chrome.storage.local.get('key', result => { ... });`
    *   **新版**: `const result = await chrome.storage.local.get('key');`

*   **`executeScript` API 变更**:
    *   `chrome.tabs.executeScript` 被 `chrome.scripting.executeScript` 取代。
    *   API 的结构也发生了变化，现在接受一个包含 `target`, `files`, 或 `func` 的对象。
        ```javascript
await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          files: ['content-script.js']
        });
```

## 5. 针对 Vue 3 + Ant Design Vue 项目的配置清单

1.  **打包工具配置 (`vite.config.ts` / `webpack.config.js`)**:
    *   **入口点**: 确保为 Popup, Options, Content Scripts, Service Worker 等分别配置了正确的入口。
    *   **输出**: 配置输出路径，确保 `manifest.json` 能正确引用打包后的文件。
    *   **CSP 兼容**: 确保构建过程是 AOT 编译，避免 `unsafe-eval`。
    *   **静态资源**: 对于需要注入到 `"world": "MAIN"` 的脚本，配置打包工具将其作为静态资源原样复制，而不是通过 HMR 加载。

2.  **`manifest.json` 配置**:
    *   `"manifest_version": 3`
    *   `"background": { "service_worker": "path/to/sw.js" }`
    *   `"content_security_policy": { ... }` (根据需要配置，但尽量保持最严格)
    *   `"host_permissions": [ "https://*.example.com/*" ]` (替代了 V2 的可选权限，明确声明需要访问的域名)
    *   `"action": { "default_popup": "popup.html" }` (替代了 `browser_action` / `page_action`)

3.  **开发流程**:
    *   对于大部分 UI 开发（如 Popup, Options 页面），Vite 的 HMR 开发模式工作良好。
    *   对于涉及 **Content Script 注入 (`"world": "MAIN"`)** 或 **Service Worker** 的功能，**必须通过 `npm run build` 生成生产包，并加载 `dist` 文件夹进行测试**，以确保其行为与最终发布版本一致。g.ts` / `webpack.config.js`)**:
    *   **入口点**: 确保为 Popup, Options, Content Scripts, Service Worker 等分别配置了正确的入口。
    *   **输出**: 配置输出路径，确保 `manifest.json` 能正确引用打包后的文件。
    *   **CSP 兼容**: 确保构建过程是 AOT 编译，避免 `unsafe-eval`。
    *   **静态资源**: 对于需要注入到 `"world": "MAIN"` 的脚本，配置打包工具将其作为静态资源原样复制，而不是通过 HMR 加载。

2.  **`manifest.json` 配置**:
    *   `"manifest_version": 3`
    *   `"background": { "service_worker": "path/to/sw.js" }`
    *   `"content_security_policy": { ... }` (根据需要配置，但尽量保持最严格)
    *   `"host_permissions": [ "https://*.example.com/*" ]` (替代了 V2 的可选权限，明确声明需要访问的域名)
    *   `"action": { "default_popup": "popup.html" }` (替代了 `browser_action` / `page_action`)

3.  **开发流程**:
    *   对于大部分 UI 开发（如 Popup, Options 页面），Vite 的 HMR 开发模式工作良好。
    *   对于涉及 **Content Script 注入 (`"world": "MAIN"`)** 或 **Service Worker** 的功能，**必须通过 `npm run build` 生成生产包，并加载 `dist` 文件夹进行测试**，以确保其行为与最终发布版本一致。